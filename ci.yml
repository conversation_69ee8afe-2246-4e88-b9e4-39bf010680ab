Global:
  version: '2.0'
  group_email: <EMAIL>
Default:
  profile:
    - buildProduction
Profiles:
  - profile:
    name: buildProduction
    mode: AGENT
    environment:
      image: DECK_CENTOS7U5_K3
      tools:
        - nodejs: 18.3.0
    build:
      command: |
        sh scripts/build.sh
    artifacts:
      release: true
  - profile:
    name: buildDevelopment
    mode: AGENT
    environment:
      image: DECK_CENTOS7U5_K3
      tools:
        - nodejs: 18.3.0
    build:
      command: |
        sh scripts/build.sh
    artifacts:
      release: true
