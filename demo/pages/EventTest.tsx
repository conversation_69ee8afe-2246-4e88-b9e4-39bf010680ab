import React, { useState } from 'react';

const EventTest: React.FC = () => {
  const [logs, setLogs] = useState<string[]>([]);
  const [formData, setFormData] = useState({ username: '', email: '' });

  const addLog = (message: string) => {
    setLogs(prev => [...prev, `[${new Date().toLocaleTimeString()}] ${message}`]);
  };

  const handleClick = (buttonName: string) => {
    addLog(`点击事件: ${buttonName}`);
  };

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    addLog(`表单提交: ${JSON.stringify(formData)}`);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const triggerError = () => {
    addLog('即将触发JavaScript错误');
    setTimeout(() => {
      const obj = null;
      // @ts-ignore
      obj.nonExistentMethod();
    }, 100);
  };

  const triggerCustomEvent = () => {
    addLog('触发自定义事件');
    const event = new CustomEvent('custom-monitor-event', {
      detail: { message: '这是自定义事件', timestamp: new Date().toISOString() }
    });
    document.dispatchEvent(event);
  };

  return (
    <div>
      <h1>页面事件测试</h1>

      <div className="card">
        <h2>点击事件测试</h2>
        <button className="button" onClick={() => handleClick('按钮1')}>按钮1</button>
        <button className="button" onClick={() => handleClick('按钮2')}>按钮2</button>
      </div>

      <div className="card">
        <h2>表单事件测试</h2>
        <form onSubmit={handleFormSubmit}>
          <div style={{ marginBottom: 10 }}>
            <label style={{ display: 'block', marginBottom: 5 }}>用户名:</label>
            <input
              type="text"
              name="username"
              value={formData.username}
              onChange={handleInputChange}
              style={{ padding: 8, width: '100%', maxWidth: 300 }}
            />
          </div>

          <div style={{ marginBottom: 10 }}>
            <label style={{ display: 'block', marginBottom: 5 }}>邮箱:</label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              style={{ padding: 8, width: '100%', maxWidth: 300 }}
            />
          </div>

          <button className="button" type="submit">提交表单</button>
        </form>
      </div>

      <div className="card">
        <h2>错误测试</h2>
        <button className="button danger" onClick={triggerError}>触发JavaScript错误</button>
      </div>

      <div className="card">
        <h2>自定义事件测试</h2>
        <button className="button" onClick={triggerCustomEvent}>触发自定义事件</button>
      </div>

      <div className="card">
        <h2>日志输出</h2>
        <div className="log-panel">
          {logs.length === 0 ? <p>暂无日志...</p> : (
            logs.map((log, index) => <div key={index}>{log}</div>)
          )}
        </div>
      </div>
    </div>
  );
};

export default EventTest;
