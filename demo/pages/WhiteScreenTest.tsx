import React, { useState } from 'react';

const WhiteScreenTest: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [showError, setShowError] = useState(false);
  const [showWhiteScreen, setShowWhiteScreen] = useState(false);

  const simulateLoading = () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
    }, 3000);
  };

  const simulateError = () => {
    setShowError(true);
    setShowWhiteScreen(false);
  };

  const simulateWhiteScreen = () => {
    setShowWhiteScreen(true);
    setShowError(false);

    // 清空页面内容模拟白屏
    const rootElement = document.getElementById('root');
    if (rootElement) {
      const originalContent = rootElement.innerHTML;
      rootElement.innerHTML = '<div style="height: 100vh; width: 100%;"></div>';

      // 5秒后恢复
      setTimeout(() => {
        rootElement.innerHTML = originalContent;
        setShowWhiteScreen(false);
      }, 5000);
    }
  };

  if (showError) {
    throw new Error('测试渲染错误');
  }

  return (
    <div>
      <h1>白屏测试</h1>

      <div className="card">
        <h2>白屏测试场景</h2>
        <p>以下按钮可以模拟各种可能导致白屏的场景：</p>

        <button
          className="button"
          onClick={simulateLoading}
          disabled={isLoading}
        >
          {isLoading ? '加载中...' : '模拟长时间加载'}
        </button>

        <button className="button danger" onClick={simulateError}>
          模拟渲染错误
        </button>

        <button className="button danger" onClick={simulateWhiteScreen}>
          模拟白屏（5秒）
        </button>
      </div>

      {isLoading && (
        <div className="card">
          <h3>加载中...</h3>
          <div style={{ display: 'flex', justifyContent: 'center', padding: '20px 0' }}>
            <div style={{
              width: '40px',
              height: '40px',
              border: '5px solid #f3f3f3',
              borderTop: '5px solid #3498db',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite'
            }}></div>
          </div>
          <style>
            {`
              @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
              }
            `}
          </style>
        </div>
      )}
    </div>
  );
};

export default WhiteScreenTest;
