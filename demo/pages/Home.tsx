import React from 'react';

const Home: React.FC = () => {
  return (
    <div>
      <h1>BCE Monitor React Demo</h1>

      <div className="card">
        <h2>功能测试</h2>
        <p>这是一个用于测试BCE Monitor功能的React演示应用。你可以通过导航栏访问不同的功能测试页面：</p>

        <ul>
          <li><strong>API监控测试</strong>：测试成功和失败的API调用监控</li>
          <li><strong>页面事件测试</strong>：测试点击、表单提交等事件的监控</li>
          <li><strong>路由切换</strong>：通过切换路由测试页面浏览监控</li>
          <li><strong>白屏测试</strong>：测试页面白屏检测能力</li>
        </ul>
      </div>

      <div className="card">
        <h2>监控日志</h2>
        <p>所有监控数据将会发送到 <code>/api/monitor/save</code> 端点，你可以在浏览器控制台中查看这些请求。</p>
      </div>
    </div>
  );
};

export default Home;
