import React, { useState } from 'react';

const ApiTest: React.FC = () => {
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    setLogs(prev => [...prev, `[${new Date().toLocaleTimeString()}] ${message}`]);
  };

  const handleSuccessApi = async () => {
    addLog('发送成功API请求');
    try {
      const response = await fetch('/api/success', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ test: true })
      });
      const data = await response.json();
      addLog(`成功响应: ${JSON.stringify(data)}`);
    } catch (error) {
      addLog(`请求错误: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  const handleErrorApi = async () => {
    addLog('发送失败API请求');
    try {
      const response = await fetch('/api/error', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ test: true })
      });
      const data = await response.json();
      addLog(`错误响应: ${JSON.stringify(data)}`);
    } catch (error) {
      addLog(`请求错误: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  const handleNetworkError = () => {
    addLog('发送网络错误请求');
    fetch('/non-existent-endpoint')
      .then(res => res.json())
      .then(data => addLog(`响应: ${JSON.stringify(data)}`))
      .catch(error => addLog(`网络错误: ${error instanceof Error ? error.message : String(error)}`));
  };

  return (
    <div>
      <h1>API监控测试</h1>

      <div className="card">
        <h2>请求测试</h2>
        <button className="button" onClick={handleSuccessApi}>发送成功请求</button>
        <button className="button danger" onClick={handleErrorApi}>发送失败请求</button>
        <button className="button danger" onClick={handleNetworkError}>触发网络错误</button>
      </div>

      <div className="card">
        <h2>日志输出</h2>
        <div className="log-panel">
          {logs.length === 0 ? <p>暂无日志...</p> : (
            logs.map((log, index) => <div key={index}>{log}</div>)
          )}
        </div>
      </div>
    </div>
  );
};

export default ApiTest;
