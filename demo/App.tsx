import React from 'react';
import { BrowserRouter, Routes, Route, Link } from 'react-router-dom';
import Home from './pages/Home';
import ApiTest from './pages/ApiTest';
import EventTest from './pages/EventTest';
import WhiteScreenTest from './pages/WhiteScreenTest';

const App: React.FC = () => {
  return (
    <BrowserRouter>
      <div className="app-container">
        <nav>
          <Link to="/">首页</Link>
          <Link to="/api">API监控测试</Link>
          <Link to="/events">页面事件测试</Link>
          <Link to="/white-screen">白屏测试</Link>
        </nav>

        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/api" element={<ApiTest />} />
          <Route path="/events" element={<EventTest />} />
          <Route path="/white-screen" element={<WhiteScreenTest />} />
        </Routes>
      </div>
    </BrowserRouter>
  );
};

export default App;
