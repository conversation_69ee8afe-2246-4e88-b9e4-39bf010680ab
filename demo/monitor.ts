import Monitor from '../packages/bce-monitor/src';
import monitorPage from '../packages/bce-monitor-page/src';
import monitorApi from '../packages/bce-monitor-api/src';

export function setupMonitor() {
  const monitor = new Monitor({
    time: 0.5, // 请求时间0.5s
    url: '/api/monitor/save',
    serviceId: 'demo-react-app',
    contentType: 'application/json',
    // 插件
    plugins: [
      // API监控
      monitorApi({}),
      // 页面浏览
      monitorPage.pageView(),
      // 错误收集
      monitorPage.pageError(),
      // 事件收集
      monitorPage.pageEvent({
        config: {
          workOrder: true
        }
      }),
      // 性能
      monitorPage.pagePerformance()
    ]
  });

  try {
    monitor.run();
    console.log('BCE Monitor initialized successfully');
  } catch (err) {
    console.error('BCE Monitor initialization failed:', err);
  }

  return monitor;
}
