export default [
  {
    url: '/api/success',
    method: 'post',
    response: () => {
      return {
        success: true,
        data: {
          message: '操作成功',
          timestamp: new Date().toISOString()
        }
      };
    }
  },
  {
    url: '/api/error',
    method: 'post',
    response: () => {
      return {
        code: 400,
        success: false,
        status: 400,
        message: '操作失败',
        timestamp: new Date().toISOString()
      };
    }
  },
  {
    url: '/api/monitor/save',
    method: 'post',
    response: req => {
      console.log('[监控数据上报]:', req.body);
      return {
        success: true
      };
    }
  }
];
