import { record } from 'rrweb';
import { IndexedDBFactory } from '@baiducloud/bce-monitor-helper';

declare const window: Window & {
  snapshotEvent: (() => any) | null;
};

function monitorSnapshot(
  { checkoutEveryNms, snapshotTime, config } = {
    checkoutEveryNms: 10000,
    snapshotTime: 20000,
    config: {}
  }
) {
  let events: any[] = [];
  let snapshot;
  const db = new IndexedDBFactory({ name: 'monitor-snapshot' });
  const getSnapshotEvent = async function () {
    await db.init();
    await db.clear();
    let store: any = await db!.read();
    let length = Math.round(snapshotTime! / checkoutEveryNms) - 1;
    let result = (store['monitor-snapshot'] && store['monitor-snapshot'].slice(-length)) || [];
    let screenShot: Array<any> = [];
    result.forEach(item => {
      screenShot = screenShot.concat(item.events);
    });
    screenShot = screenShot.concat(events);
    return screenShot;
  };
  return {
    name: 'monitor-snapshot',
    install: async () => {
      await db.init();
      await db.clear();
      snapshot = record({
        emit(event, isCheckout) {
          if (isCheckout) {
            db?.add({
              time: new Date(),
              events
            });
            events = [];
          }
          // 将 event 存入 events 数组中
          events.push(event);
        },
        checkoutEveryNms,
        ...config
      });
      /**
       * 获取录屏数据
       * @returns 录屏数据
       */
      window.snapshotEvent = getSnapshotEvent;
    },

    getSnapshotEvent,

    uninstall: () => {
      if (db) {
        db.clear();
      }
      window.snapshotEvent = null;
      if (snapshot) {
        snapshot();
      }
    }
  };
}

export default monitorSnapshot;
