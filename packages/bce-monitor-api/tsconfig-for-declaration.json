{"compilerOptions": {"module": "commonjs", "target": "es5", "lib": ["ES6", "DOM", "ES2015", "ES2021"], "sourceMap": true, "jsx": "react", "moduleResolution": "node", "rootDir": "./src", "baseUrl": "./src", "importHelpers": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "sourceRoot": "src", "noImplicitReturns": true, "noImplicitThis": true, "noImplicitAny": true, "strictNullChecks": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "skipLibCheck": true}, "include": ["src"], "exclude": ["./lib", "./example", "./__tests__", "./esm"], "types": ["node", "typePatches"]}