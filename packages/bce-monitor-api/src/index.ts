declare global {
  interface XMLHttpRequest {
    monitorInfo: {
      url: string;
      method: string;
      headers: any;
      requestData: any;
    };
  }
  interface Window {
    snapshotEvent: (() => any) | null;
  }
}

interface ApiInfo {
  url: string; // 不带参数的url
  completeUrl: string; // 完整的url
  method: string; // 请求类型
  status: number; // 响应状态码
  apiTime: number; // 请求时间
  timeStamp: number; // 请求时间
  protocol: string; // 请求协议
  requestData: string; // 请求数据
  requestHeader: any; // 请求头
  responseData: string; // 响应数据
  responseSize: number; // 响应数据大小
  responseHeader: {
    requestId?: string | null; // requestId
    // 缓存策略
    expires?: string | null;
    cacheControl?: string | null;
    etag?: string | null;
    lastModify?: string | null;
    compressionPolicy?: string | null; // 压缩策略
    corsPolicy?: string | null; // 跨域策略
  };
  performance: {
    startTime: number; // 记录开始时间
    redirectStart: number; // 重定向开始时间
    redirectEnd: number; // 重定向结束时间
    fetchStart: number; // 浏览器发起HTTP请求时间
    domainLookupStart: number; // DNS查询开始时间
    domainLookupEnd: number; // DNS查询结束时间
    connectStart: number; // TCP连接开始时间
    connectEnd: number; // TCP连接结束时间
    secureConnectionStart: number; // 浏览器跟服务器建立安全连接的时间。
    requestStart: number; // 浏览器向服务器开始发送数据的时间。
    responseStart: number; // 服务器向浏览器开始发送数据的时间。
    responseEnd: number; // 服务器向浏览器结束发送数据的时间。
    serverTiming?: any; // 服务器耗时记录
  };
  isError?: boolean;
  errorInfo?: {errorStatus: string; [key: string]: string};
  snapshotEvent?: any;
}

interface MonitorApiProps {
  isError?: (responseData: string) => Promise<{errorStatus: string} | void>; // 是否立即上报
  isNotReport?: (data: any) => boolean; // 是否取消上报
  extraData?: Record<string, any>; // 额外数据
  excludes?: string[]; // 不上报的接口
  responseHeader?: '*' | string[]; // 响应头
}

const xhr = XMLHttpRequest;
const _originSetRequestHeader = xhr.prototype.setRequestHeader;
const _originOpen = xhr.prototype.open;
const _originSend = xhr.prototype.send;
const _originFetch = window.fetch;

function monitorApi({isError, isNotReport, extraData, excludes, responseHeader}: MonitorApiProps) {
  const urlExcludes = ['/api/mc/letter', '/api/log/csi', '/api/sys/mon/save'];
  urlExcludes.push(...(excludes || []));

  return {
    name: 'monitor-api',
    install: (collector, reporter) => {
      window.fetch = async (...args) => {
        const monitorInfo: any = {};
        try {
          if (typeof args[0] === 'string' || args[0] instanceof URL) {
            const fetchOption: RequestInit = args[1] || {};
            monitorInfo.url = typeof args[0] === 'string' ? args[0] : args[0].href;
            monitorInfo.method = fetchOption.method || 'GET';
            monitorInfo.requestData =
              typeof fetchOption.body === 'object' ? JSON.stringify(fetchOption.body) : fetchOption.body || '';
            if (fetchOption.headers && fetchOption.headers instanceof Headers) {
              monitorInfo.requestHeader = {};
              for (let key in fetchOption.headers.keys()) {
                monitorInfo.requestHeader[key] = fetchOption.headers.get(key);
              }
            } else {
              monitorInfo.requestHeader = fetchOption.headers || {};
            }
          } else if (args[0] instanceof Request) {
            const _request: Request = args[0].clone();
            monitorInfo.url = _request.url;
            monitorInfo.method = _request.method || 'GET';
            monitorInfo.requestHeader = {};
            for (let key in _request.headers.keys()) {
              monitorInfo.requestHeader[key] = _request.headers.get(key);
            }
            try {
              // 无法获悉请求数据类型
              monitorInfo.requestData = await _request.text();
            } catch {}
          }
        } catch {}
        return _originFetch.apply(window, args).then(async (response: Response) => {
          // 拿不到请求url或者是上报请求，不上报
          if (!monitorInfo.url || (monitorInfo.requestHeader && monitorInfo.requestHeader['IS_BCE_MONITOR'])) {
            return response;
          }
          // 排除的接口直接请求，不上报
          for (let url of urlExcludes) {
            if (monitorInfo.url.includes(url)) {
              return response;
            }
          }

          try {
            // response只能被消费一次，所以clone一份，消费副本，把原始数据返回
            const _response = response.clone();
            const apiTime = Date.now();
            const resIsJson = (_response.headers.get('Content-Type') || '').includes('application/json');
            // 无法获悉响应数据类型
            monitorInfo.responseData = resIsJson ? (await _response.text()) || '{}' : '{}';
            const fetchEnd = () => {
              // 稍微延迟下，保证能获取到正确的接口性能数据
              setTimeout(async () => {
                const resource: any = [].concat(
                  // @ts-ignore
                  window.resource,
                  JSON.parse(JSON.stringify(performance.getEntriesByType('resource')))
                );
                // 获取当前请求的性能信息
                let i = resource.length - 1;
                for (; i >= 0; i--) {
                  if (resource[i].name === _response.url) {
                    break;
                  }
                }
                const currentResource = i > -1 ? resource[i] : {};
                delete monitorInfo.requestHeader.csrftoken;
                // 整理上报数据
                let reportData: ApiInfo = {
                  url: monitorInfo.url.replace(/(\?|\#).*$/, ''),
                  completeUrl: monitorInfo.url,
                  method: monitorInfo.method,
                  status: currentResource.responseStatus || 200,
                  apiTime: apiTime,
                  timeStamp: apiTime,
                  protocol: currentResource.nextHopProtocol,
                  requestData: monitorInfo.requestData,
                  requestHeader: monitorInfo.requestHeader,
                  responseData: monitorInfo.responseData,
                  responseSize: currentResource.transferSize,
                  responseHeader: {},
                  performance: {
                    startTime: currentResource.startTime,
                    redirectStart: currentResource.redirectStart,
                    redirectEnd: currentResource.redirectEnd,
                    fetchStart: currentResource.fetchStart,
                    domainLookupStart: currentResource.domainLookupStart,
                    domainLookupEnd: currentResource.domainLookupEnd,
                    connectStart: currentResource.connectStart,
                    connectEnd: currentResource.connectEnd,
                    secureConnectionStart: currentResource.secureConnectionStart,
                    requestStart: currentResource.requestStart,
                    responseStart: currentResource.responseStart,
                    responseEnd: currentResource.responseEnd,
                    serverTiming: currentResource.serverTiming
                  }
                };
                if (extraData && typeof extraData === 'object') {
                  reportData = {
                    ...reportData,
                    ...extraData
                  };
                }
                const responseHeaderTag = {
                  requestId: 'X-Bce-Request-Id',
                  expires: 'Expires',
                  cacheControl: 'Cache-Control',
                  etag: 'ETag',
                  lastModify: 'Last-Modify',
                  compressionPolicy: 'Content-Encoding',
                  corsPolicy: 'Access-Control-Allow-Origin'
                };
                if (responseHeader === '*') {
                  for (let key in _response.headers.keys()) {
                    reportData.responseHeader[key] = _response.headers.get(key);
                  }
                } else if (responseHeader) {
                  responseHeader.forEach(key => {
                    const v = _response.headers.get(key.toLocaleLowerCase());
                    if (v) {
                      reportData.responseHeader[key] = v;
                    }
                  });
                } else {
                  for (let key in responseHeaderTag) {
                    const tag: string = responseHeaderTag[key];
                    const v = _response.headers.get(tag.toLocaleLowerCase());
                    if (v) {
                      reportData.responseHeader[key] = v;
                    }
                  }
                }
                // 业务判断是否要上报
                if (
                  isNotReport &&
                  isNotReport({
                    ...reportData,
                    response: monitorInfo.responseData
                  })
                ) {
                  return;
                }
                // 如果是错误请求就立刻上报
                const error = isError && (await isError(monitorInfo.responseData));
                if (error || _response.status >= 400 || !_response.ok) {
                  reportData.isError = true;
                  if (error) {
                    reportData.errorInfo = error;
                  }
                  reporter({'monitor-api': [reportData]});
                } else {
                  reportData.responseData = '{}';
                  reportData.requestData = '{}';
                  // 将数据塞进上报数据队列里面
                  collector(reportData);
                }
              }, 0);
            };
            fetchEnd();
          } finally {
            return response;
          }
        });
      };

      xhr.prototype.setRequestHeader = function (name, value) {
        const headers = this.monitorInfo?.headers || {};
        this.monitorInfo = Object.assign(this.monitorInfo || {}, {
          headers: Object.assign(headers || {}, {[name]: value})
        });
        _originSetRequestHeader.apply(this, [name, value]);
      };
      // 记录请求方法、地址
      xhr.prototype.open = function (...params) {
        this.monitorInfo = {
          method: params[0],
          url: params[1],
          headers: null,
          requestData: null
        };
        _originOpen.apply(this, params as any);
      };
      // 重写send方法，监听请求返回
      xhr.prototype.send = function (data) {
        this.monitorInfo.requestData = data;
        const apiTime = Date.now();
        const ajaxEnd = () => {
          // 稍微延迟下，保证能获取到正确的接口性能数据
          setTimeout(async () => {
            const resource: any = [].concat(
              // @ts-ignore
              window.resource,
              JSON.parse(JSON.stringify(performance.getEntriesByType('resource')))
            );
            // 获取当前请求的性能信息
            let i = resource.length - 1;
            for (; i >= 0; i--) {
              if (resource[i].name === this.responseURL) {
                break;
              }
            }
            const currentResource = i > -1 ? resource[i] : {};
            const requestHeader = Object.assign({}, this.monitorInfo.headers);
            delete requestHeader.csrftoken;
            const responseHeaderObj = {};

            (this.getAllResponseHeaders() || '').split('\r\n').forEach(n => {
              const kv = n.split(':');
              kv[0] && (responseHeaderObj[kv[0]] = kv[1]?.trim?.());
            });
            const resIsJson = (responseHeaderObj['content-type'] || '').includes('application/json');
            const responseData = resIsJson ? this.response : '{}';
            // 整理上报数据
            let reportData: ApiInfo = {
              url: this.monitorInfo.url.replace(/(\?|\#).*$/, ''),
              completeUrl: this.monitorInfo.url,
              method: this.monitorInfo.method,
              status: currentResource.responseStatus || 200,
              apiTime: apiTime,
              timeStamp: apiTime,
              protocol: currentResource.nextHopProtocol,
              requestData: this.monitorInfo.requestData,
              requestHeader: requestHeader,
              responseData: responseData,
              responseSize: currentResource.transferSize,
              responseHeader: {},
              performance: {
                startTime: currentResource.startTime,
                redirectStart: currentResource.redirectStart,
                redirectEnd: currentResource.redirectEnd,
                fetchStart: currentResource.fetchStart,
                domainLookupStart: currentResource.domainLookupStart,
                domainLookupEnd: currentResource.domainLookupEnd,
                connectStart: currentResource.connectStart,
                connectEnd: currentResource.connectEnd,
                secureConnectionStart: currentResource.secureConnectionStart,
                requestStart: currentResource.requestStart,
                responseStart: currentResource.responseStart,
                responseEnd: currentResource.responseEnd,
                serverTiming: currentResource.serverTiming
              }
            };
            if (extraData && typeof extraData === 'object') {
              reportData = {
                ...reportData,
                ...extraData
              };
            }
            const responseHeaderTag = {
              requestId: 'X-Bce-Request-Id',
              expires: 'Expires',
              cacheControl: 'Cache-Control',
              etag: 'ETag',
              lastModify: 'Last-Modify',
              compressionPolicy: 'Content-Encoding',
              corsPolicy: 'Access-Control-Allow-Origin'
            };

            if (responseHeader === '*') {
              reportData.responseHeader = responseHeaderObj;
            } else if (responseHeader) {
              responseHeader.forEach(key => {
                const v = responseHeaderObj[key.toLocaleLowerCase()];
                if (v) {
                  reportData.responseHeader[key] = v;
                }
              });
            } else {
              for (let key in responseHeaderTag) {
                const tag: string = responseHeaderTag[key];
                const v = responseHeaderObj[tag.toLocaleLowerCase()];
                if (v) {
                  reportData.responseHeader[key] = v;
                }
              }
            }

            // 业务判断是否要上报
            if (
              isNotReport &&
              isNotReport({
                ...reportData,
                response: responseData
              })
            ) {
              return;
            }
            // 如果是错误请求就立刻上报
            const error = isError && (await isError(responseData));
            if (error || this.status >= 400) {
              reportData.isError = true;
              if (error) {
                reportData.errorInfo = error;
              }
              // 接口报错录屏先不上报
              // if (window.snapshotEvent) {
              //   reportData.snapshotEvent = await window.snapshotEvent();
              // }
              reporter({'monitor-api': [reportData]});
            } else {
              reportData.responseData = '{}';
              reportData.requestData = '{}';
              // 将数据塞进上报数据队列里面
              collector(reportData);
            }
          }, 0);
        };

        // 如果是上报的请求，则忽略监听
        if (this.monitorInfo.headers && this.monitorInfo.headers['IS_BCE_MONITOR']) {
          return _originSend.apply(this, [data]);
        }
        // 排除的接口直接请求，不上报
        for (let url of urlExcludes) {
          if (this.monitorInfo.url.includes(url)) {
            return _originSend.apply(this, [data]);
          }
        }

        this.addEventListener('loadend', ajaxEnd, false);

        return _originSend.apply(this, [data]);
      };
    },
    uninstall: () => {
      xhr.prototype.setRequestHeader = _originSetRequestHeader;
      xhr.prototype.open = _originOpen;
      xhr.prototype.send = _originSend;
    }
  };
}

export default monitorApi;
