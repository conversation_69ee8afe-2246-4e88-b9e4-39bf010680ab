declare global {
  interface Window {
    Monitor: any;
    $context: any;
    store: any;
    monitorSnapshot: any;
    monitorApi: any;
    monitorPage: any;
    MonitorInstalled: any;
    G_APP_NAME: any;
  }
}

// const MODULES = ['bce-monitor', 'bce-monitor-api', 'bce-monitor-page', 'bce-monitor-snapshot'];
const MODULES = ['bce-monitor', 'bce-monitor-api', 'bce-monitor-page'];

const BUILD_VERSION = '{{BUILD_VERSION}}';

// copy from fe-framework
function getAppName(pathname: string) {
  // 如果指定了 pathname 应该忽略 G_APP_NAME 的内容
  // 比如 helper.getAppName('/') 的时候，虽然 G_APP_NAME 是 bcc，但是我们也希望获取 home 这一个返回值
  // return new URLSearchParams(location.search).get('serviceType');
  if (pathname == null && typeof window.G_APP_NAME !== 'undefined') {
    // 如果页面中存在 G_APP_NAME 变量，说明 build 的时候就生成了，可以直接返回
    return window.G_APP_NAME;
  }

  // 1. '/', '/home/'
  // 2. '/bcc/'
  // 3. '/x-home/', '/x-bcc/'
  let path = pathname || location.pathname;
  let nextSlashIndex = path.indexOf('/', 1);
  let appName = nextSlashIndex === -1 ? path.substr(1) : path.substring(1, nextSlashIndex);
  appName = appName
    .replace(/index\.html/, '')
    .replace(/\//g, '')
    .replace(/^x\-/, '');
  return appName || 'home';
}

// 获取环境
function getENV() {
  const env = location.host.indexOf('.bce.baidu.com') > -1 ? 'consoleOnline' : 'consoleOffline';
  return env;
}

function isLocal() {
  return /localhost/.test(location.hostname);
}

// 获取语言环境
function getCurrentLanguage() {
  let locale = /locale=([a-zA-Z-]+)/g.exec(location.search);
  let localeCookie = /bce-locale=([^;]+);?/.exec(document.cookie)?.[1];
  let language = (locale || [])[1] || localeCookie;
  if (!language) {
    language = sessionStorage.getItem('LANGUAGE')?.replace(/"/g, '') || navigator.language;
  }
  language = language?.toLowerCase();
  return language;
}

// 加载 js 文件
function loader(src: string) {
  return new Promise((resolve, reject) => {
    const script = document.createElement('script');
    script.src = src;
    document.body.appendChild(script);
    script.onload = () => {
      resolve(true);
    };

    script.onerror = () => {
      reject(new Error('加载失败'));
    };
  });
}

function monitorJs(name: string) {
  return `https://bce.bdstatic.com/lib/@baiducloud/bce-track/${BUILD_VERSION}/${name}/dist/index.js`;
}

async function main() {
  const sessions = Object.keys(sessionStorage);
  const appName = getAppName(location.pathname);
  const notReportKey = [`_${appName}_version_`, 'X-Bce-Access-Version', `_${appName}_pagemaker_version_`];
  const curEnv = getENV();
  const isOnline = curEnv === 'consoleOnline';
  if (window.MonitorInstalled) {
    return;
  }

  for (let key of sessions) {
    if (notReportKey.includes(key) && sessionStorage.getItem(key)) {
      return;
    }
  }
  //本地调试线上不上报，本地调试沙盒上报
  if (isLocal() && isOnline) {
    return;
  }
  const res = await Promise.all(MODULES.map(module => loader(monitorJs(module))));
  if (res && window.Monitor) {
    window.MonitorInstalled = true;
    const monitor = new window.Monitor({
      time: 0.5,
      url: '/api/sys/mon/save',
      serviceId: getAppName(location.pathname),
      customInfo: function () {
        return {
          userId: () => {
            const cookie = /bce-login-accountid=([^;]+);?/.exec(document.cookie);
            const userId =
              cookie?.[1] ||
              window.$context?.getUserId() ||
              window.store?.app?.frameworkData?.constants?.userid ||
              window.store?.variable?.appVariables?.frameworkContext?.userId;
            return userId;
          },
          subUserId: () => {
            const cookie = /bce-login-userid=([^;]+);?/.exec(document.cookie);
            const userId =
              cookie?.[1] ||
              window.$context?.getUserId() ||
              window.store?.app?.frameworkData?.constants?.userid ||
              window.store?.variable?.appVariables?.frameworkContext?.userId;
            return userId;
          },
          env: getENV(),
          locale: getCurrentLanguage
        };
      },
      plugins: [
        // window.monitorSnapshot(),
        window.monitorApi({
          isError: function (res: any) {
            try {
              var data = typeof res === 'string' ? JSON.parse(res) : res;
              if (data.success === false || data.success === 'false') {
                return {errorStatus: data.status};
              }
            } catch (err) {
              console.log(err);
            }
          },
          isNotReport: function (reportData: any) {
            if (reportData.responseHeader.requestId && !!~reportData.responseHeader.requestId.indexOf('bcecanarytag')) {
              return true;
            }
          }
        }),
        window.monitorPage.pageView(),
        window.monitorPage.pageError(),
        window.monitorPage.pageEvent({
          config: {
            workOrder: true
          }
        }),
        window.monitorPage.pagePerformance()
      ]
    });
    monitor.run();
  }
}

try {
  main();
} catch (error) {
  console.error('监控脚本加载失败:', error);
}

export {};
