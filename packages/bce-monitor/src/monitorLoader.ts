// 获取环境
function isConsoleOnline() {
  return location.host.indexOf('.bce.baidu.com') > -1;
}

// 加载 js 文件
function loader(src: string) {
  return new Promise((resolve, reject) => {
    const script = document.createElement('script');
    script.src = src;
    document.body.appendChild(script);
    script.onload = () => {
      resolve(true);
    };

    script.onerror = () => {
      reject(new Error('加载失败'));
    };
  });
}

function main() {
  if (isConsoleOnline()) {
    loader('https://bce.bdstatic.com/lib/@baiducloud/bce-track/@latest/monitor.js');
  } else {
    loader('https://bce.bdstatic.com/lib/@baiducloud/bce-track/@latest/monitor.dev.js');
  }
}

try {
  main();
} catch (error) {
  console.error('监控脚本加载失败:', error);
}
