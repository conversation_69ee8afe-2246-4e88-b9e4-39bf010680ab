declare global {
  interface Window {
    Monitor: any;
    $context: any;
    store: any;
    monitorSnapshot: any;
    monitorApi: any;
    monitorPage: any;
    G_APP_NAME: any;
  }
}

const MODULES = ['bce-monitor', 'bce-monitor-api', 'bce-monitor-page', 'bce-monitor-snapshot'];

const BUILD_VERSION = '{{BUILD_VERSION}}';

// 获取环境
function getENV() {
  const env = location.host.indexOf('qasandbox') > -1 ? 'consoleOffline' : 'consoleOnline';
  return env;
}

// 加载 js 文件
function loader(src: string) {
  return new Promise((resolve, reject) => {
    const script = document.createElement('script');
    script.src = src;
    document.body.appendChild(script);
    script.onload = () => {
      resolve(true);
    };

    script.onerror = () => {
      reject(new Error('加载失败'));
    };
  });
}

function monitorJs(name: string) {
  return `https://bce.bdstatic.com/lib/@baiducloud/bce-track/${BUILD_VERSION}/${name}/dist/index.js`;
}

async function main() {
  if (location.host.indexOf('localhost') > -1) {
    return;
  }
  const res = await Promise.all(MODULES.map(module => loader(monitorJs(module))));
  if (res && window.Monitor) {
    const monitor = new window.Monitor({
      time: 0.5,
      url: '/api/monitor/api/sys/mon/save',
      headers: {
        'CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'),
        'Content-Type': 'application/json; charset=utf-8'
      },
      serviceId: 'pagemaker',
      customInfo: function () {
        return {
          userId: () => window.store.user.email,
          env: getENV()
        };
      },
      plugins: [
        // window.monitorSnapshot(),
        window.monitorApi({
          isError: function (res: any) {
            try {
              var data = JSON.parse(res);
              if (data.success === false || (data.status && data.status !== 200)) {
                return {errorStatus: data.status, errorMsg: data.msg};
              }
            } catch (err) {
              console.log(err);
            }
          }
        }),
        window.monitorPage.pageView(),
        window.monitorPage.pageError(),
        window.monitorPage.pageEvent(),
        window.monitorPage.pagePerformance()
      ]
    });
    monitor.run();
  }
}

try {
  main();
} catch (error) {
  console.error('监控脚本加载失败:', error);
}

export {};
