function wrapEvent(type) {
  const orig = window.history[type];
  return function () {
    const rv = orig.apply(this, arguments);
    const e = new Event(type);
    e.arguments = arguments;
    window.dispatchEvent(e);
    return rv;
  };
}
function historyRouterListener(callback) {
  window.history.pushState = wrapEvent('pushState');
  window.history.replaceState = wrapEvent('replaceState');
  window.addEventListener('pushState', callback);
  window.addEventListener('replaceState', callback);
  window.addEventListener('popstate', callback);
}

export { historyRouterListener };
