import cloneDeep from 'lodash/cloneDeep';
import {uuidv4} from '@baiducloud/bce-monitor-helper';
import monitorBaseInfo from '@baiducloud/bce-monitor-base-info';
import {historyRouterListener} from './utils';
import MD5 from 'crypto-js/md5';

interface MonitorPlugin {
  name: string; // 插件名称
  install: (collect: (data: any) => void, report: (data: any) => void) => void; // 插件初始化，collect是采集数据收集函数
  uninstall?: () => void; // 插件注销
  preReport?: (data: any) => any; // 上报数据前钩子，data是所有的上报数据，返回上报数据
  // 页面的生命周期
  pageComplete?: (data: any, e: any, pluginReporter: any) => any; // 页面初始化完成
  pageOnload?: (data: any, e: any, pluginReporter: any) => any; // 页面渲染完成
  pageHashChange?: (data: any, e: any, pluginReporter: any, config: any) => any; // 页面hash变化
  pageHistoryChange?: (data: any, e: any, pluginReporter: any, config: any) => any; // 页面history变化
  pageUnload?: (data: any, e: any, pluginReporter: any) => any; // 页面关闭
}

interface MonitorSDKConfig {
  serviceId: string; // 产品id
  url?: string; // 埋点的接口
  time?: number; // 上报的间隔时长, 默认每秒上报一次
  headers?: {[key: string]: string}; // 自定义请求头，如果加了请求头就只能走ajax
  plugins?: MonitorPlugin[]; // 埋点插件
  contentType?: string;
  customInfo?: () => {[key: string]: string}; // 自定义数据
}

declare global {
  interface Window {
    resource: any[];
    signature: string;
  }
}

window.resource = [];

class Monitor {
  plugins: MonitorPlugin[] = [];
  data: {[key: string]: any} = {};
  config: MonitorSDKConfig;
  timeid: any = null;
  busy = false;
  waiteReportData: {[key: string]: any}[] = [];
  _oldUrl: string = location.host + location.pathname + location.hash;
  oldUrl: string = location.href;

  static DEFAULT_CONTENT_TYPE = 'text/plain;charset=UTF-8';

  constructor(config: MonitorSDKConfig) {
    this.config = config;

    // 增加签名，同一次会话签名是一致的
    if (!window.signature) {
      window.signature = uuidv4();
    }

    // 默认使用baseInfo插件
    this.plugins = [
      monitorBaseInfo({
        customInfo: () => {
          const customInfo = config.customInfo ? config.customInfo() : {};
          return Object.assign(
            {
              serviceId: config.serviceId,
              signature: window.signature
            },
            customInfo
          );
        }
      }),
      ...(this.config.plugins || [])
    ];

    // 资源满了之后需要使用一个数组缓存一下
    performance.addEventListener('resourcetimingbufferfull', () => {
      window.resource = window.resource.concat(JSON.parse(JSON.stringify(performance.getEntriesByType('resource'))));
      performance.clearResourceTimings();
    });
  }
  /**
   * 注册所有的插件，开启定时上报
   */
  run() {
    // 如果环境是{MONITOR_ENV}，说明没有替换，不上报内容
    if (this.config?.customInfo?.()?.env === '{MONITOR_ENV}') {
      return;
    }
    // 上报上次遗留的数据
    this.reportLeftData();
    // 页面生命周期注册
    this.pageCycler();
    // 安装插件
    this.plugins.forEach(plugin => plugin.install(this.collector(plugin.name), this.pluginReporter(this)));

    // 定时上报
    this.reportByTime();
  }

  /**
   * 上报之前遗留的数据
   */
  reportLeftData() {
    try {
      const leftData = JSON.parse(localStorage.getItem('monitorData') || '{}');
      if (Object.keys(leftData).length > 0) {
        this.reporter(leftData);
      }
    } catch (error) {}
  }
  /**
   * 页面生命周期
   */
  pageCycler() {
    // 页面加载完成
    document.addEventListener('readystatechange', e => {
      if (document.readyState === 'complete') {
        this.plugins.forEach(plugin => {
          if (plugin.pageComplete) {
            this.data = plugin.pageComplete(this.data, e, this.pluginReporter(this)) || this.data;
          }
        });
      }
    });
    // 页面渲染完成
    window.addEventListener('load', e => {
      this.plugins.forEach(plugin => {
        if (plugin.pageOnload) {
          this.data = plugin.pageOnload(this.data, e, this.pluginReporter(this)) || this.data;
        }
      });
      this.reportData();
    });

    // 页面hash改变
    window.addEventListener('hashchange', e => {
      this.plugins.forEach(plugin => {
        if (plugin.pageHashChange) {
          this.data =
            plugin.pageHashChange(this.data, e, this.pluginReporter(this), {
              _oldUrl: this._oldUrl,
              oldUrl: this.oldUrl
            }) || this.data;
        }
      });
      this._oldUrl = location.host + location.pathname + location.hash;
      this.oldUrl = location.href;
      this.reportData();
    });

    // 页面history改变
    historyRouterListener(e => {
      this.plugins.forEach(plugin => {
        if (plugin.pageHistoryChange) {
          this.data =
            plugin.pageHistoryChange(this.data, e, this.pluginReporter(this), {
              _oldUrl: this._oldUrl,
              oldUrl: this.oldUrl
            }) || this.data;
        }
      });
      this._oldUrl = location.host + location.pathname + location.hash;
      this.oldUrl = location.href;
      this.reportData();
    });

    // 浏览器关闭前
    window.addEventListener('unload', e => {
      this.plugins.forEach(plugin => {
        if (plugin.pageUnload) {
          this.data = plugin.pageUnload(this.data, e, this.pluginReporter(this)) || this.data;
        }
      });
      this.reportData();
    });
  }

  /**
   * 收集所有的数据
   * @param name 插件名称
   */
  collector(name: string) {
    return (data: any) => {
      if (this.data[name]) {
        this.data[name].push(data);
      } else {
        this.data[name] = [data];
      }
      // 将数据也存一份到本地缓存中，上报之后会清空
      let leftData = cloneDeep(this.data);
      for (const plugin of this.plugins) {
        if (plugin.preReport) {
          leftData = plugin.preReport(leftData);
        }
      }
      localStorage.setItem('monitorData', JSON.stringify(leftData));
    };
  }
  /**
   * 默认每分钟上报一次数据
   */
  reportByTime() {
    if (this.timeid) {
      clearTimeout(this.timeid);
    }
    this.timeid = setTimeout(
      () => {
        this.reportData();
        this.reportByTime();
      },
      1000 * 60 * (this.config.time || 1)
    );
  }
  /**
   * 上报数据前会，遍历所有插件的preReport函数
   */
  async reportData() {
    let data = cloneDeep(this.data);
    this.data = {};
    if (Object.keys(data).length === 0) {
      return;
    }
    for (const plugin of this.plugins) {
      if (plugin.preReport) {
        data = plugin.preReport(data);
      }
    }
    await this.reporter(data);
    // 上报完数据清空下缓存
    localStorage.setItem('monitorData', '');
  }
  /**
   * 处理authToken,防止模拟请求
   * @param data 上报的数据
   */
  initAuthToken(data) {
    const baseInfo = data['monitor-base-info'];
    const signature = baseInfo.signature;
    baseInfo.uuid = uuidv4();
    // 生成 authToken
    baseInfo.authToken = MD5(
      `token=id${baseInfo.uuid}&serviceId${baseInfo.serviceId}&signature${signature}`
    ).toString();
    return data;
  }
  /**
   * 优先使用sendBeacon，降级使用xhr
   * @param data 上报的数据
   */
  async reporter(data: any) {
    // 如果请求处于繁忙中，先将数据存在队列中，等空闲了再上传
    if (this.busy) {
      this.waiteReportData.push(data);
      return;
    }
    this.busy = true;
    const url = this.config.url || '';
    try {
      data = this.initAuthToken(data);
    } catch (error) {
      console.log('initAuthToken error', error);
    }
    const stringData = JSON.stringify(data);

    if (navigator.sendBeacon && stringData.length < 50000 && !this.config.headers) {
      const type = this.config.contentType || Monitor.DEFAULT_CONTENT_TYPE;
      const headers = {type};
      const blob = new Blob([stringData], headers);
      await navigator.sendBeacon(url, blob);
    } else {
      await this.ajax({
        url,
        data
      });
    }
    this.busy = false;
    // 如果缓存队列中有数据，再发起一次上报请求
    if (this.waiteReportData.length > 0) {
      // TODO:如果缓存队列数据过多，需要分次请求
      const waiteReportData = cloneDeep(this.waiteReportData);
      this.waiteReportData = [];
      const data = {};
      waiteReportData.forEach(item => {
        for (let key in item) {
          if (!data[key]) {
            data[key] = item[key];
          } else {
            if (Array.isArray(item[key])) {
              data[key].push(...item[key]);
            } else {
              Object.assign(data[key], item[key]);
            }
          }
        }
      });
      await this.reporter(data);
    }
  }
  /**
   * 封装promise ajax请求
   */
  ajax(config: any) {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      xhr.open('POST', config.url);
      xhr.withCredentials = true;
      if (this.config.headers) {
        Object.keys(this.config.headers).forEach(key => {
          xhr.setRequestHeader(key, this.config.headers![key]);
        });
      }
      if (this.config.contentType) {
        xhr.setRequestHeader('content-type', this.config.contentType);
      }
      xhr.setRequestHeader('IS_BCE_MONITOR', '1');
      xhr.send(JSON.stringify(config.data));
      xhr.onload = () => {
        resolve(xhr);
      };
    });
  }
  /**
   * 插件独立上报数据
   * @param data 上报的数据
   */
  pluginReporter(self: any) {
    return (data: any) => {
      let reportData = data;
      for (const plugin of self.plugins) {
        if (plugin.preReport) {
          reportData = plugin.preReport(reportData);
        }
      }
      this.reporter(reportData);
    };
  }
  /**
   * 注销埋点
   */
  uninstall() {
    clearTimeout(this.timeid);
    // 上报残留的数据
    this.reportData();
    // 注销所有插件
    for (const plugin of this.plugins) {
      if (plugin.uninstall) {
        plugin.uninstall();
      }
    }
  }
}

export default Monitor;
