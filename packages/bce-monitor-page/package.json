{"name": "@baiducloud/bce-monitor-page", "version": "0.0.6", "description": "百度智能云前端监控-页面监控插件", "keywords": ["api"], "author": "qin<PERSON>yan <<EMAIL>>", "license": "ISC", "main": "lib/index.js", "module": "esm/index.js", "directories": {"lib": "lib"}, "files": ["lib", "esm"], "repository": {"type": "git", "url": "ssh://qin<PERSON><EMAIL>:8235/baidu/baiducloud/bce-track"}, "scripts": {"clean-dist": "rimraf lib/* esm/* dist/*", "build": "npm run clean-dist && NODE_ENV=production rollup -c && npm run declaration", "dev": "rollup -c -w", "declaration": "tsc --project tsconfig-for-declaration.json --allowJs --declaration --emitDeclarationOnly --declarationDir ./lib  --rootDir ./src || true"}, "devDependencies": {"@rollup/plugin-commonjs": "^22.0.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^13.3.0", "@rollup/plugin-terser": "^0.4.1", "@rollup/plugin-typescript": "^8.3.2", "@types/lodash": "^4.14.191", "rimraf": "^5.0.0", "rollup": "^2.73.0", "rollup-plugin-license": "^2.7.0", "tslib": "^2.3.1", "typescript": "^4.6.4"}, "dependencies": {"@baiducloud/bce-monitor-helper": "^0.0.1-beta.9", "lodash": "^4.17.21"}}