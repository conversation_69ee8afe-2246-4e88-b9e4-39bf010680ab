// rollup.config.js
import commonjs from '@rollup/plugin-commonjs';
import json from '@rollup/plugin-json';
import resolve from '@rollup/plugin-node-resolve';
import typescript from '@rollup/plugin-typescript';
import terser from '@rollup/plugin-terser';
import { main, module } from './package.json';
import path from 'path';

const input = ['./src/index.ts'];
const dist = 'dist/index.js';
const external = ['lodash/cloneDeep', '@baiducloud/bce-monitor-helper'];
const env = process.env.NODE_ENV;

export default [
  {
    input,
    output: [
      {
        dir: path.dirname(main),
        format: 'cjs',
        exports: 'named',
        preserveModulesRoot: './src',
        preserveModules: false // Keep directory structure and files
      }
    ],
    plugins: getPlugins('cjs'),
    external
  },
  {
    input,
    output: [
      {
        dir: path.dirname(module),
        format: 'esm',
        exports: 'named',
        preserveModulesRoot: './src',
        preserveModules: false // Keep directory structure and files
      }
    ],
    plugins: getPlugins('esm'),
    external
  },
  {
    input,
    output: [
      {
        dir: path.dirname(dist),
        format: 'iife',
        name: 'monitorPage'
      }
    ],
    plugins: getPlugins('iife')
  }
];

function getPlugins(format = 'esm') {
  const typeScriptOptions = {
    typescript: require('typescript'),
    sourceMap: false,
    outputToFilesystem: true,
    target: 'es5',
    ...(format === 'esm'
      ? {
          compilerOptions: {
            rootDir: './src',
            outDir: path.dirname(module)
          }
        }
      : format === 'cjs'
      ? {
          compilerOptions: {
            rootDir: './src',
            outDir: path.dirname(main)
          }
        }
      : {
          compilerOptions: {
            rootDir: './src',
            outDir: path.dirname(dist)
          }
        })
  };

  return [
    json(),
    resolve({
      jsnext: true,
      main: true
    }),
    typescript(typeScriptOptions),
    commonjs({
      sourceMap: false
    }),
    format === 'iife' && env === 'production' ? terser() : null
  ].filter(n => n);
}
