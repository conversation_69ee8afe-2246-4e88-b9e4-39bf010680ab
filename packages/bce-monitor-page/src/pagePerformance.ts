interface PagePerformanceOptions {}

declare global {
  interface Window {
    resource: PerformanceResourceTiming[];
  }
}

function pagePerformance(options?: PagePerformanceOptions) {
  let lcp;
  let lcpObserve;
  // 这里主要是为了处理fmp和onload时机不一致的问题，需要等到fmp和onload都完成后再上报
  async function main(reporter) {
    lcpFn();
    const [fmpData, onload] = await Promise.all([fmpFn(), onloadFn()]);
    const { fmp, fmpIdentifier } = fmpData as any;
    const { fp, fcp } = getFpAndFcp();
    // 静态资源信息
    const resourceTiming = getResourceTiming();

    reporter({
      'monitor-page-performance': {
        type: 'page-performance',
        timeStamp: Date.now(),
        fp,
        fcp,
        fmpIdentifier,
        fmp,
        lcp,
        onload,
        resourceTiming
      }
    });
  }

  // 获取所有静态资源的信息
  function getResourceTiming() {
    const resource = window.resource.concat(performance.getEntriesByType('resource') as PerformanceResourceTiming[]);
    const resourceTiming = resource.map(entry => ({
      name: entry.name,
      initiatorType: entry.initiatorType,
      startTime: entry.startTime,
      duration: entry.duration
    }));
    return resourceTiming;
  }

  // 获取fp和fcp
  function getFpAndFcp() {
    // @ts-ignore
    const paintEntries = performance.getEntries('paint');
    // 首次渲染像素点时间
    const fp = paintEntries.find(entry => entry.name == 'first-paint')?.startTime;
    // 首次渲染元素时间
    const fcp = paintEntries.find(entry => entry.name == 'first-contentful-paint')?.startTime;
    return { fp, fcp };
  }

  // 有效元素渲染时间
  function fmpFn() {
    return new Promise((resolve, reject) => {
      const fmpObserve = new MutationObserver(recordList => {
        for (let mutation of recordList) {
          const target = mutation.target as HTMLElement;
          const id = target.id;
          if (id === 'main' && mutation.addedNodes.length > 0) {
            if (mutation.addedNodes[0].childNodes.length > 0 || mutation.addedNodes[0].nodeName === '#text') {
              fmpObserve.disconnect();
              resolve({
                fmpIdentifier: 'main',
                fmp: performance.now()
              });
            }
            return;
          }
        }
      });
      fmpObserve.observe(document, {
        childList: true, // 监听子节点变化（如果subtree为true，则包含子孙节点）
        subtree: true // 整个子树的所有节点
      });
    });
  }

  // 页面最大元素渲染时间
  function lcpFn() {
    lcpObserve = new PerformanceObserver(entry => {
      const entries = entry.getEntries();
      const lastEntry = entries[entries.length - 1];
      if (lastEntry) {
        lcp = lastEntry.startTime;
      }
    });
    lcpObserve.observe({
      type: 'largest-contentful-paint',
      buffered: true
    });
  }

  // load时间
  function onloadFn() {
    return new Promise((resolve, reject) => {
      window.addEventListener('load', () => {
        resolve(performance.now());
      });
    });
  }

  // First Input Delay 首次输入延迟 (FID)
  function fidFn(reporter) {
    const fidObserve = new PerformanceObserver(entry => {
      const eventList = entry.getEntries();
      eventList.forEach(event => {
        reporter({
          'monitor-page-performance': {
            type: 'page-performance-fid',
            timeStamp: Date.now(),
            fid: event.startTime,
            lcp
          }
        });
      });
      // 用户首次交互后注销lcp
      lcpObserve.disconnect();
      fidObserve.disconnect();
    });
    fidObserve.observe({ type: 'first-input', buffered: true });
  }

  return {
    name: 'monitor-performance',
    install: (selector, reporter) => {
      main(reporter);
      fidFn(reporter);
    }
  };
}

export default pagePerformance;
