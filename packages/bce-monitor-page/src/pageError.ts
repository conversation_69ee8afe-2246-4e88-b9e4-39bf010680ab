import {uuidv4} from '@baiducloud/bce-monitor-helper';
declare global {
  interface Window {
    snapshotEvent: (() => any) | null;
    monitorCatcher: (error: any, errorInfo: any) => void;
  }
}

function pageError(container?: string[]) {
  // 需要判断为空的元素
  let containerElements = ['#main', '#react-main', '.cxd-AppLayout-content'].concat(container || []);
  let reporterTemp;
  let reportPage: string[] = [];
  let errorFlag;
  let timer;

  function checkPageError() {
    const emptyPage = containerElements.every(item => {
      const dom = document.querySelector(item);
      return !dom || (dom && !dom.childElementCount);
    });
    // 处理pagemaker的情况
    if (document.querySelector('.cxd-Alert--danger.AppPage')) {
      return true;
    } else if (emptyPage) {
      return true;
    } else {
      return false;
    }
  }

  function errorFn(event) {
    const page = location.href;
    let times = 0; // 记录判断为白屏的次数
    // 如果该页面已经上报过白屏或者正在打点检查，就不再重复上报
    if (reportPage.includes(page) || timer || !event.message) {
      return;
    }
    let errorInfo = {
      message: event.message || ''
    };
    if (event.error) {
      errorInfo['stack'] = event.error.stack;
    }

    function report() {
      reportPage.push(page);
      errorFlag = {
        id: uuidv4(),
        page
      };
      const data = {
        type: 'monitor-page-error',
        error: errorInfo,
        timestamp: Date.now(),
        errorId: errorFlag.id
      };
      if (window.snapshotEvent) {
        window.snapshotEvent().then(event => {
          data['snapshotEvent'] = event;
          reporterTemp({
            'monitor-page-error': data
          });
        });
      } else {
        reporterTemp({
          'monitor-page-error': data
        });
      }
    }

    function check() {
      if (checkPageError()) {
        recheckError();
      } else {
        timer = null;
      }
    }

    function recheckError() {
      // 连续5s都判断为白屏，就上报
      if (times >= 5) {
        report();
        timer = null;
      } else {
        times++;
        timer = setTimeout(check, 1000);
      }
    }

    check();
  }

  return {
    name: 'bce-monitor-page-error',
    install: (selector, reporter) => {
      reporterTemp = reporter;
      window.addEventListener('error', errorFn, true);
      window.monitorCatcher = (error, errorInfo) => {
        errorFn({
          message: errorInfo,
          error: {
            stack: error
          }
        });
      };
    },
    uninstall: () => {
      window.removeEventListener('error', errorFn);
    },
    pageHashChange: () => {
      timer && clearTimeout(timer);
      timer = null;
    },
    pageOnload: (data, e, reporter) => {
      setTimeout(() => {
        const page = location.href;
        // 如果渲染完成了，并且检测到没有白屏，并且当前页面上报过白屏，就重新上报一次取消白屏的接口
        if (!checkPageError() && errorFlag?.page === page) {
          reporter({
            'monitor-page-error-cancel': {
              type: 'monitor-page-error-cancel',
              errorId: errorFlag.id
            }
          });
          // 清空错误标志
          const index = reportPage.indexOf(page);
          reportPage.splice(index, 1);
          errorFlag = null;
        }
      }, 2000);
    }
  };
}

export default pageError;
