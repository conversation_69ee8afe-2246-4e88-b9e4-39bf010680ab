function getRealOnClickDom(el: HTMLElement) {
  if (!el) {
    return;
  }
  const keys = Object.keys(el);
  const fiberId = keys.find(key => key.startsWith('__reactFiber'));
  if (fiberId) {
    const onClick = el[fiberId].memoizedProps.onClick || function () {};
    const strFn = onClick.toString();
    if (/\{.*\S+.*}/.test(strFn)) {
      return el;
    } else {
      return getRealOnClickDom(el[fiberId].return.stateNode);
    }
  } else {
    return;
  }
}

export function getReactClickDom(e: PointerEvent) {
  const el = (e.target || e.srcElement) as HTMLElement;
  return getRealOnClickDom(el);
}
