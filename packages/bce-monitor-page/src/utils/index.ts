const rAF = (function () {
  return (
    window.requestAnimationFrame ||
    window.webkitRequestAnimationFrame ||
    function (callback) {
      window.setTimeout(callback, 1000 / 60);
    }
  );
})();

/**
 * 计算字符串的哈希值
 *
 * @param str 要计算哈希值的字符串
 * @param seed 哈希计算的种子，默认为0x811c9dc5
 * @returns 返回计算出的哈希值
 */
function hash(str, seed?) {
  var i,
    l,
    hval = seed === undefined ? 0x811c9dc5 : seed;

  for (i = 0, l = str.length; i < l; i++) {
    hval ^= str.charCodeAt(i);
    hval += (hval << 1) + (hval << 4) + (hval << 7) + (hval << 8) + (hval << 24);
  }
  return ('0000000' + (hval >>> 0).toString(16)).substr(-8);
}

export { rAF, hash };

export * from './reactEvent';
