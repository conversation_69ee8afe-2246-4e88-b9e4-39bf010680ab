import debounce from 'lodash/debounce';

interface PageViewOptions {}

// 页面跳转 或 关闭时上报PV
function pageView(options?: PageViewOptions) {
  let reporterTemp;
  let oldURL;
  let currentUrl;
  let enterTime = Date.now();
  let stayTime = 0;

  function visibleFn() {
    if (document.visibilityState === 'visible') {
      enterTime = Date.now();
    } else {
      stayTime += Date.now() - enterTime;
    }
  }

  function formatUrl(url: string) {
    return url ? url.replace(/\?[^#]*/g, '') : '';
  }

  const pageViewReporter = debounce((event: {oldURL: string}) => {
    const stayTimeValue = stayTime + (Date.now() - enterTime) || 0;

    if (stayTimeValue < 200) {
      return;
    }
    reporterTemp &&
      reporterTemp({
        'monitor-pv': {
          type: 'monitor-pv',
          href: formatUrl(event.oldURL),
          from: formatUrl(oldURL),
          timestamp: Date.now(),
          stayTime: stayTimeValue
        }
      });
    enterTime = Date.now();
    stayTime = 0;
    oldURL = event.oldURL;
  }, 200);
  return {
    name: 'bce-monitor-pv',
    install: (collector, reporter) => {
      reporterTemp = reporter;
      document.addEventListener('visibilitychange', visibleFn);
    },
    uninstall: () => {
      window.removeEventListener('visibilitychange', visibleFn);
    },
    // 因为监控的是路由改变后，所以改下baseInfo里的url
    preReport: data => {
      if (data['monitor-pv'] && currentUrl) {
        data['monitor-base-info'].href = currentUrl;
        data['monitor-base-info']._href = formatUrl(currentUrl);
      }
      return data;
    },
    pageOnload: (data: any) => {
      oldURL = document.referrer;
      enterTime = Date.now();
      stayTime = 0;
      return data;
    },
    pageHashChange: (data: any, e: any) => {
      currentUrl = e.oldURL;
      pageViewReporter(e);
    },
    pageHistoryChange: (data: any, e: any, _this, urlData) => {
      currentUrl = urlData.oldUrl;
      pageViewReporter({
        oldURL: urlData.oldUrl
      });
    },
    pageUnload: (data: any, e: any) => {
      currentUrl = location.href;
      reporterTemp({
        'monitor-pv': {
          type: 'monitor-pv',
          href: formatUrl(location.href),
          from: formatUrl(oldURL || document.referrer),
          timestamp: Date.now(),
          stayTime: stayTime + (Date.now() - enterTime) || 0
        }
      });
    }
  };
}

export default pageView;
