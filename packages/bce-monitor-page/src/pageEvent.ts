import {hash, getReactClickDom} from './utils';

interface PageEventProps {
  click?: Function[];
  config?: {workOrder?: boolean};
}

interface MonitorEventReportProps {
  eventType?: string; // 事件类型，默认click
  trackId: string; // 用户行为唯一标识
  trackName: string; // 用户行为名称
  xpath?: string; // 元素xpath
  innerText?: string; // 元素包含的内容
  eventData?: any; // 其他内容
}

declare global {
  interface Element {
    __addEventListener: any;
    __removeEventListener: any;
  }
  interface Window {
    monitorEventReport: (props: MonitorEventReportProps) => void;
  }
}

/**
 * console工单事件点击
 */
function consoleTicketClick(e, selector, reporter) {
  if (e.target.tagName === 'A') {
    const url: string = e.target.href || '';
    if (url.includes('ticket.bce.baidu.com') && url.includes('ticket/create')) {
      const requestId = /requestId=([^&]*)/.exec(url)?.[1];
      reporter({
        'monitor-event': {
          type: 'monitor-event',
          eventType: 'ticket-jump',
          timeStamp: Date.now(),
          eventData: {
            requestId: requestId || '',
            timestamp: Date.now()
          }
        }
      });
    }
  }
}

/**
 * 带trackId的a标签、按钮点击
 */
function trackIdTagAClick(e, selector, reporter) {
  if (e.target.getAttribute('data-track-id')) {
    const a = e.target;
    selector({
      type: 'monitor-event',
      eventType: 'click',
      trackId: a.getAttribute('data-track-id'),
      trackName: a.getAttribute('data-track-name'),
      xpath: hash(a.getAttribute('data-track-id')), // 内容太长，用hash保证唯一性
      innerText: a.innerHTML, // 文本或图标内容
      timeStamp: Date.now(),
      eventData: {
        timestamp: Date.now()
      }
    });
  }
}

function pageEvent(props?: PageEventProps) {
  let {click, config} = props || {};
  const {workOrder} = config || {};
  if (workOrder) {
    if (!click) {
      click = [];
    }
    click = click.concat(consoleTicketClick, trackIdTagAClick);
  }

  function pageEventInit(selector, reporter) {
    const clickFnMap = new Set();
    let eventArr: any[] = [];

    /**
     * 排除html、body、#main等绑定的点击事件
     */
    function checkValidDom(dom: HTMLElement | null) {
      if (!dom || !dom.tagName) {
        return false;
      }
      if (['HTML', 'BODY'].includes(dom.tagName)) {
        return false;
      } else if (dom.id === 'main') {
        return false;
      }
      return dom;
    }

    function clickSelector(eventArr) {
      const xpath = eventArr.map(item => item.xpath).join('+');
      const innerText = eventArr
        .map(item => item.innerText)
        .filter(n => n)
        .join('+');
      const trackId = eventArr.find(item => item.trackId)?.trackId;
      const trackName = eventArr.find(item => item.trackName)?.trackName;
      const eventData = eventArr.find(item => item.eventData)?.eventData;

      const data = {
        type: 'monitor-event',
        eventType: 'click',
        trackId,
        trackName,
        xpath: hash(xpath), // 内容太长，用hash保证唯一性
        innerText, // 文本或图标内容
        timeStamp: Date.now(),
        eventData: {
          timestamp: Date.now(),
          ...eventData
        }
      };
      selector(data);
    }

    function clickFn({
      e,
      xpath,
      trackId,
      trackName,
      innerText,
      eventData
    }: {
      e: PointerEvent;
      xpath: string;
      trackId?: string;
      trackName?: string;
      innerText?: string;
      eventData?: any;
    }) {
      if (eventArr.find(n => n.timeStamp === e.timeStamp)) {
        eventArr.push({
          timeStamp: e.timeStamp,
          xpath,
          trackId,
          trackName,
          innerText,
          eventData
        });
      } else {
        eventArr.push({
          timeStamp: e.timeStamp,
          xpath,
          trackId,
          trackName,
          innerText,
          eventData
        });
        setTimeout(() => {
          clickSelector(eventArr);
          // 重置
          eventArr = [];
        }, 10);
      }
    }

    function clickEvent(e: PointerEvent) {
      const dom = checkValidDom(e.currentTarget as HTMLElement) || getReactClickDom(e);

      // trackId存在已上报
      if (dom && !e.target?.getAttribute('data-track-id')) {
        const pageX = e.pageX;
        const pageY = e.pageY;
        const innerWidth = window.innerWidth;
        const innerHeight = window.innerHeight;
        const xPer = Number((pageX / innerWidth).toFixed(3));
        const yPer = Number((pageY / innerHeight).toFixed(3));

        const composedPath = e.composedPath();
        const index = composedPath.findIndex(d => d == dom);
        const pathArr = composedPath.slice(index) as HTMLElement[];
        const xpath =
          dom.innerHTML +
          '/' +
          pathArr
            .map(p => p.tagName)
            .filter(n => n)
            .join('/');
        const track = dom.dataset.trackId
          ? {trackId: dom.dataset.trackId, trackName: dom.dataset.trackName}
          : undefined;
        let innerText = dom.innerText;
        if (dom.tagName === 'svg') {
          innerText = dom.outerHTML;
        }
        clickFn({
          e,
          xpath,
          trackId: track?.trackId,
          trackName: track?.trackName,
          innerText,
          eventData: {xPer, yPer, pageX, pageY}
        });
      }
    }

    // 重写事件注册函数
    Element.prototype.__addEventListener = Element.prototype.addEventListener;
    Element.prototype.addEventListener = function (type, listener, useCapture = false) {
      this.__addEventListener(type, listener, useCapture);

      if (type === 'click' && !clickFnMap.has(this)) {
        clickFnMap.add(this);
        this.__addEventListener(type, clickEvent, useCapture);
      }
    };

    // 重写事件注销函数
    Element.prototype.__removeEventListener = Element.prototype.removeEventListener;
    Element.prototype.removeEventListener = function (type, listener) {
      this.__removeEventListener(type, listener);
      if (type === 'click') {
        clickFnMap.delete(this);
        this.__removeEventListener(type, clickEvent);
      }
    };
  }

  return {
    name: 'monitor-event',
    install: (selector, reporter) => {
      if (click && click.length > 0) {
        window.document.addEventListener('click', e => {
          click?.forEach(fn => fn(e, selector, reporter));
        });
      }
      pageEventInit(selector, reporter);
      window.monitorEventReport = (
        {eventType, trackId, trackName, xpath, innerText, eventData}: MonitorEventReportProps,
        immediate = false // 立即上报
      ) => {
        const data = {
          type: 'monitor-event',
          eventType: eventType || 'click',
          trackId,
          trackName,
          xpath: hash(xpath || trackId), // 内容太长，用hash保证唯一性
          innerText, // 文本或图标内容
          timeStamp: Date.now(),
          eventData: {
            timestamp: Date.now(),
            ...eventData
          }
        };
        if (immediate) {
          reporter({
            'monitor-event': [data]
          });
        } else {
          selector(data);
        }
      };
    }
  };
}

export default pageEvent;
