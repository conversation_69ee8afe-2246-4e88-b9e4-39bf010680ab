import { rAF } from './utils';
interface PageFpsOptions {
  fpsLimit?: number;
}

function pageFps(options: PageFpsOptions) {
  const { fpsLimit = 20 } = options || {};
  let lastTime = performance.now();
  let frame = 0;
  let lowFpsCount = 0;
  let stop = false;
  const loop = reporter => {
    const now = performance.now();
    frame++;
    // 每秒采集一次
    if (now > lastTime + 1000) {
      let fps = Math.round((frame * 1000) / (now - lastTime));
      // 帧率小于20记录一次
      if (fps < fpsLimit) {
        lowFpsCount++;
      }
      // 3s帧率都小于20，则上报
      if (lowFpsCount > 3) {
        reporter({
          pfs: fps,
          time: now
        });
        lowFpsCount = 0;
      }
      lastTime = now;
      frame = 0;
    }
    if (!stop) {
      rAF(loop);
    }
  };
  return {
    name: 'bce-monitor-page-fps',
    install: (collector, reporter) => {
      stop = false;
      loop(reporter);
    },
    uninstall: () => {
      stop = true;
    }
  };
}

export default pageFps;
