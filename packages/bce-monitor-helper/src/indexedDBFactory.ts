class IndexedDBFactory {
  public db: IDBDatabase | undefined;
  public dbName: string = '';
  /**
   * @param config: {name: string} 表名
   */

  constructor({ name }: { name: string }) {
    this.dbName = name;
  }
  /**
   * 初始化数据库
   */
  public async init() {
    if (window.indexedDB) {
      return new Promise((resolve, reject) => {
        if (this.db) {
          resolve(this);
        }
        const request = window.indexedDB.open(this.dbName);
        request.onsuccess = e => {
          this.db = (e.target as any).result;
          resolve(this);
        };
        request.onerror = e => {
          reject(e);
        };

        // 创建数据库以及版本更新时调用
        request.onupgradeneeded = e => {
          this.db = (e.target as any).result;
          if (!this.db?.objectStoreNames.contains(this.dbName)) {
            // createObjectStore只能在versionChange事件中调用
            this.db && this.db.createObjectStore(this.dbName, { autoIncrement: true });
          }
        };
      });
    }
    return null;
  }

  /**
   * 根据表名获取store
   */
  private getStore(): IDBObjectStore | null {
    if (this.db) {
      const transaction = this.db.transaction(this.dbName, 'readwrite');
      return transaction.objectStore(this.dbName);
    }

    return null;
  }

  /**
   * 根据表名读取数据
   */
  public async read() {
    return new Promise((resolve, reject) => {
      let result: { [key: string]: any } = {};
      const store = this.getStore();
      if (store) {
        const request = store.openCursor();
        request.onsuccess = e => {
          const cursor = (e.target as any).result;
          if (cursor) {
            result[this.dbName] = result[this.dbName] || [];
            result[this.dbName].push(cursor.value);
            cursor.continue();
          } else {
            resolve(result);
          }
        };

        request.onerror = e => {
          reject(e);
        };
      }
    });
  }

  /**
   * 添加错误日志到数据库中
   *
   * @param {object} data 数据
   */
  public add(data: any) {
    if (!this.db) {
      return;
    }
    const store = this.getStore();
    store && store.add(data);
  }
  /**
   * 清空数据
   */
  public async clear() {
    return new Promise<any>((resolve, reject) => {
      const store = this.getStore();
      const clearRequest = store?.clear();
      clearRequest!.onerror = e => {
        reject(e);
      };

      clearRequest!.onsuccess = () => {
        resolve(true);
      };
    });
  }
}

export default IndexedDBFactory;
