import IndexedDBFactory from './indexedDBFactory';

// 参考 https://github.com/streamich/v4-uuid
const str = () => ('00000000000000000' + (Math.random() * 0xffffffffffffffff).toString(16)).slice(-16);

const uuidv4 = () => {
  const a = str();
  const b = str();
  return a.slice(0, 8) + '-' + a.slice(8, 12) + '-4' + a.slice(13) + '-a' + b.slice(1, 4) + '-' + b.slice(4);
};

export { uuidv4, IndexedDBFactory };
