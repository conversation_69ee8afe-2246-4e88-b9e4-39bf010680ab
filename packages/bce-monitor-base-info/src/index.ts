import isEmpty from 'lodash/isEmpty';

interface BaseInfoProps {
  customInfo?: () => {[key: string]: any};
}

interface BaseInfo {
  _href: string; // 域名 + 路径 + hash
  href: string; // 完整路由
  locale: string; // 语言
  browser: string; // 浏览器类型
  browserVersion: string; // 浏览器版本
  browserHeight: number; // 浏览器高度
  browserWidth: number; // 浏览器宽度
  platform: string; // 操作系统
  signature: string; // 签名
  [key: string]: string | number;
}

function monitorBaseInfo(config: BaseInfoProps = {}) {
  const baseInfo: BaseInfo = {} as BaseInfo;
  function getBrowserInfo() {
    const userAgent = navigator.userAgent;
    const isChrome = /Chrome\/(\d+)/.test(userAgent);
    const isFirefox = /Firefox\/(\d+)/.test(userAgent);
    const isEdge = /Edge\/(\d+)/.test(userAgent);
    const isIE = /Trident\/.+rv:(\d+)/.test(userAgent);
    const isSafari = /^((?!chrome|android).)*safari/i.test(userAgent);
    const isOpera = /OPR\/(\d+)/.test(userAgent);
    let browserVersion = '';
    let browser = '';
    if (isChrome) {
      browser = 'Chrome';
      browserVersion = userAgent.match(/Chrome\/(\d+)/)![1];
    } else if (isFirefox) {
      browser = 'Firefox';
      browserVersion = userAgent.match(/Firefox\/(\d+)/)![1];
    } else if (isEdge) {
      browser = 'Edge';
      browserVersion = userAgent.match(/Edge\/(\d+)/)![1];
    } else if (isIE) {
      browser = 'IE';
      browserVersion = userAgent.match(/rv:(\d+)/)![1];
    } else if (isSafari) {
      browser = 'Safari';
      browserVersion = userAgent.match(/Version\/(\d+)/)![1];
    } else if (isOpera) {
      browser = 'Opera';
      browserVersion = userAgent.match(/OPR\/(\d+)/)![1];
    } else {
      browser = 'Unknown browser';
    }
    return {browser, browserVersion};
  }
  return {
    name: 'monitor-base-info',
    install: () => {
      const browserInfo = getBrowserInfo();
      Object.assign(baseInfo, browserInfo);
      baseInfo.platform = navigator.platform;
    },
    preReport: data => {
      if (data['monitor-base-info']) {
        return data;
      }
      baseInfo._href = location.host + location.pathname + location.hash;
      baseInfo.href = location.href;
      baseInfo.locale = navigator.language;
      baseInfo.browserHeight = window.innerHeight;
      baseInfo.browserWidth = window.innerWidth;
      // 添加自定义数据
      if (config.customInfo) {
        const customInfo = config.customInfo();
        Object.keys(customInfo).forEach(key => {
          if (typeof customInfo[key] === 'function') {
            customInfo[key] = customInfo[key]();
          }
        });
        Object.assign(baseInfo, customInfo);
      }
      data['monitor-base-info'] = baseInfo;
      return data;
    },
    pageHistoryChange(data, hashEvent, reporter, config) {
      // 保证当前路由的数据能够正确上报
      if (!isEmpty(data)) {
        baseInfo._href = config._oldUrl;
        baseInfo.href = config.oldUrl;
        baseInfo.locale = navigator.language;
        baseInfo.browserHeight = window.innerHeight;
        baseInfo.browserWidth = window.innerWidth;

        // 添加自定义数据
        if (config.customInfo) {
          Object.assign(baseInfo, config.customInfo());
        }
        data['monitor-base-info'] = baseInfo;
        return data;
      }
    }
  };
}

export default monitorBaseInfo;
