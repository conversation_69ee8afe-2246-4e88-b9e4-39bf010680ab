/**
 * plugin声明文件
 *
 * @file plugin.d.ts
 * <AUTHOR>
 */

declare namespace Plugin {
  interface SuiTrack {
    activate(sui: Sui): void;
  }
  interface TableFilter {
    filter: any;
    field: any;
  }

  interface Listener {
    (e: TableFilter): void;
  }

  class Avatar {
    id: number;
  }

  class Badge {
    id: number;
  }

  class Button {
    id: number;
  }

  class BreadcrumbItem {
    id: number;
  }

  class Breadcrumb {
    id: number;
  }

  class Cascader {
    id: number;
  }

  class Card {
    id: number;
  }

  class CheckboxGroup {
    id: number;
  }

  class Checkbox {
    id: number;
  }

  class ColorPicker {
    id: number;
  }

  class CollapsePanel {
    id: number;
  }

  class Collapse {
    id: number;
  }

  class Dialog {
    id: number;
    __openTime: number;
  }

  class Dropdown {
    id: number;
  }

  class FormItem {
    id: number;
  }

  class Form {
    id: number;
  }

  class InputNumber {
    id: number;
    __focusTime: number;
  }

  class Input {
    id: number;
    __focusTime: number;
  }

  class Link {
    id: number;
  }

  class List {
    id: number;
  }

  class Message {
    id: number;
  }

  class Menu {
    id: number;
  }

  class Notification {
    id: number;
  }

  class OptionGroup {
    id: number;
  }

  class Option {
    id: number;
  }

  class Page {
    id: number;
  }

  class Pagination {
    id: number;
  }

  class Popover {
    id: number;
  }

  class Popup {
    id: number;
  }

  class Radio {
    id: number;
  }

  class Rate {
    id: number;
  }

  class RadioGroup {
    id: number;
  }

  class Select {
    id: number;
    __focusTime: number;
  }

  class Slider {
    id: number;
  }

  class ScrollBar {
    id: number;
  }

  class Step {
    id: number;
  }

  class Steps {
    id: number;
  }

  class Switch {
    id: number;
  }

  class TabPane {
    id: number;
  }

  class Tabs {
    id: number;
  }

  class TimeLine {
    id: number;
  }

  class TimeLineItem {
    id: number;
  }

  class TimePicker {
    id: number;
  }

  class Table {
    id: number;
  }

  class Tooltip {
    id: number;
  }

  class Transfer {
    id: number;
  }

  class Trigger {
    id: number;
  }

  class WebUploader {
    id: number;
  }

  // sui-biz
  class AppSidebar {
    id: number;
  }

  class AppSidebarItem {
    id: number;
  }

  interface SuiConstructor<T> {
    new (option?: {data?: Partial<any>}): T;
    [key: string]: any;
  }

  interface SuiBizConstructor<T> {
    new (option?: {data?: Partial<any>}): T;
    [key: string]: any;
  }

  interface Sui {
    Avatar: SuiConstructor<Avatar>;
    Badge: SuiConstructor<Badge>;
    Button: SuiConstructor<Button>;
    BreadcrumbItem: SuiConstructor<BreadcrumbItem>;
    Breadcrumb: SuiConstructor<Breadcrumb>;
    Cascader: SuiConstructor<Cascader>;
    Card: SuiConstructor<Card>;
    CheckboxGroup: SuiConstructor<CheckboxGroup>;
    Checkbox: SuiConstructor<Checkbox>;
    ColorPicker: SuiConstructor<ColorPicker>;
    CollapsePanel: SuiConstructor<CollapsePanel>;
    Collapse: SuiConstructor<Collapse>;
    Dialog: SuiConstructor<Dialog>;
    Dropdown: SuiConstructor<Dropdown>;
    FormItem: SuiConstructor<FormItem>;
    Form: SuiConstructor<Form>;
    InputNumber: SuiConstructor<InputNumber>;
    Input: SuiConstructor<Input>;
    Link: SuiConstructor<Link>;
    List: SuiConstructor<List>;
    Message: SuiConstructor<Message>;
    Menu: SuiConstructor<Menu>;
    Notification: SuiConstructor<Notification>;
    OptionGroup: SuiConstructor<OptionGroup>;
    Option: SuiConstructor<Option>;
    Page: SuiConstructor<Page>;
    Pagination: SuiConstructor<Pagination>;
    Popover: SuiConstructor<Popover>;
    Popup: SuiConstructor<Popup>;
    Radio: SuiConstructor<Radio>;
    Rate: SuiConstructor<Rate>;
    Select: SuiConstructor<Select>;
    Slider: SuiConstructor<Slider>;
    ScrollBar: SuiConstructor<ScrollBar>;
    Step: SuiConstructor<Step>;
    Steps: SuiConstructor<Steps>;
    Switch: SuiConstructor<Switch>;
    TabPane: SuiConstructor<TabPane>;
    Table: SuiConstructor<Table>;
    Tabs: SuiConstructor<Tabs>;
    TimeLine: SuiConstructor<TimeLine>;
    TimeLineItem: SuiConstructor<TimeLineItem>;
    TimePicker: SuiConstructor<TimePicker>;
    Tooltip: SuiConstructor<Tooltip>;
    Transfer: SuiConstructor<Transfer>;
    Trigger: SuiConstructor<Trigger>;
    WebUploader: SuiConstructor<WebUploader>;
  }

  interface SuiBiz {
    AppSidebar: SuiBizConstructor<AppSidebar>;
  }

  type SuiTarget = any;

  type SuiType =
    | 'Avatar'
    | 'Badge'
    | 'Button'
    | 'BreadcrumbItem'
    | 'Breadcrumb'
    | 'Cascader'
    | 'Card'
    | 'CheckboxGroup'
    | 'Checkbox'
    | 'ColorPicker'
    | 'CollapsePanel'
    | 'Collapse'
    | 'Dialog'
    | 'Dropdown'
    | 'FormItem'
    | 'Form'
    | 'InputNumber'
    | 'Input'
    | 'Link'
    | 'List'
    | 'Message'
    | 'Menu'
    | 'Notification'
    | 'OptionGroup'
    | 'Option'
    | 'Page'
    | 'Pagination'
    | 'Popover'
    | 'Popup'
    | 'Radio'
    | 'Rate'
    | 'Select'
    | 'Slider'
    | 'ScrollBar'
    | 'Step'
    | 'Steps'
    | 'Switch'
    | 'TabPane'
    | 'Table'
    | 'Tabs'
    | 'TimeLine'
    | 'TimeLineItem'
    | 'TimePicker'
    | 'Tooltip'
    | 'Transfer'
    | 'Trigger'
    | 'WebUploader';

  type SuiBizType = 'AppSidebar';
}

export = Plugin;
