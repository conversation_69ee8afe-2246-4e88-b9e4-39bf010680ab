/**
 * sui组件监控插件
 *
 * @file suiTrack.ts
 * <AUTHOR>
 */

import { Sui, SuiType, SuiTarget, SuiBiz, SuiBizType } from './types';
import { isSanCmpt } from './helper';

class SuiTrack {
  sui: Sui | null = null;
  suiBiz: SuiBiz | null = null;

  // 为了方便生成handler函数，减少代码量，平时最好不要这么写
  [key: string]: any;

  activate(sui: any, suiBiz?: any) {
    this.sui = sui;

    (Object.keys(sui) as Array<SuiType>).forEach((key: SuiType) => {
      let cmpt = sui[key];

      if (isSanCmpt(cmpt)) {
        cmpt.prototype.created = this.createdHandler.apply(this, [key]);

        let subCmpts = Object.keys(cmpt);

        if (subCmpts.length > 0) {
          subCmpts.forEach(item => {
            if (isSanCmpt(cmpt[item])) {
              cmpt[item].prototype.created = this.createdHandler.apply(this, [item as SuiType]);
            }
          });
        }
      }
    });

    if (suiBiz) {
      this.suiBiz = suiBiz;

      (Object.keys(suiBiz) as Array<SuiBizType>).forEach((key: SuiBizType) => {
        let cmpt = suiBiz[key];

        if (isSanCmpt(cmpt)) {
          cmpt.prototype.created = this.createdHandler.apply(this, [key]);

          let subCmpts = Object.keys(cmpt);

          if (subCmpts.length > 0) {
            subCmpts.forEach(item => {
              if (isSanCmpt(cmpt[item])) {
                cmpt[item].prototype.created = this.createdHandler.apply(this, [(key + item) as SuiBizType]);
              }
            });
          }
        }
      });
    }
  }

  createdHandler(key: SuiType | SuiBizType) {
    const _this = this;

    return function (this: SuiTarget) {
      _this.setTrackAttribute(this);
    };
  }

  // 这里为所有组件的dom元素添加data-track-id和data-track-name属性，monitor上报时会获取trackId进行上报
  setTrackAttribute(target: SuiTarget) {
    let trackId = target.data.get('dataTrackId') || target.data.get('trackId');
    let trackName = target.data.get('dataTrackName') || target.data.get('trackName');
    trackId && target.el && target.el.setAttribute('data-track-id', trackId);
    trackName && target.el && target.el.setAttribute('data-track-name', trackName);
  }
}

const suiTrack = new SuiTrack();

export default suiTrack;
