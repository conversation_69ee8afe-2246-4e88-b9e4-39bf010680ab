{"name": "@baiducloud/bce-monitor", "version": "0.0.0-beta.38", "description": "前端行为采集", "scripts": {"build": "npm run build --workspaces", "dev": "npm run dev --workspaces", "demo": "vite", "publish": "sh scripts/publish.sh", "version": "lerna version"}, "author": "qin<PERSON>yan <<EMAIL>>", "workspaces": ["packages/*"], "license": "ISC", "devDependencies": {"@types/node": "^18.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^6.20.0", "@typescript-eslint/parser": "^6.20.0", "@vitejs/plugin-react": "^4.0.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-simple-import-sort": "^10.0.0", "lerna": "^6.6.1", "mockjs": "^1.1.0", "prettier": "^3.2.5", "prettier-eslint": "^16.3.0", "rimraf": "^5.0.5", "rollup-watch": "^4.3.1", "typescript": "^4.8.4", "vite": "^6.2.2", "vite-plugin-mock": "^3.0.2"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.10.0"}}