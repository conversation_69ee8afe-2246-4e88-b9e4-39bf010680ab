# export PATH=$NODEJS_BIN_LATEST:$PATH

echo "node: $(node -v)"
echo "npm: v$(npm -v)"

rm -rf output
mkdir output

npm i --registry=http://registry.npm.baidu-int.com --legacy-peer-deps

echo "******************************** install success ********************************"

npx lerna run build

echo "******************************** build success ********************************"

cd packages
packages=`ls`
cd ..

echo "******************************** start copy ********************************"

for package in ${packages[@]};
do
  echo "******************************** copy ${package} ********************************"
  mkdir output/${package}
  cp -rf packages/$package/dist output/$package
done

echo "******************************** done ********************************"
