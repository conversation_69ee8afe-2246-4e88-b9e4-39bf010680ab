export PATH=$NODEJS_BIN_LATEST:$PATH

npm i

npm run build --workspaces

cd packages
packages=`ls`
cd ..

rm -rf npm
mkdir npm

for package in ${packages[@]};
do
  mkdir npm/${package}
  cp -rf packages/$package/lib npm/$package
  cp -rf packages/$package/esm npm/$package
  cp -rf packages/$package/package.json npm/$package
done

cd npm

for package in ${packages[@]};
do
  cd ${package}
  npm publish --registry=http://registry.npm.baidu-int.com
  cd ..
done