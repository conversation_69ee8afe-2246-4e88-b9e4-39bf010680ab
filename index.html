<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>BCE Monitor React Demo</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      margin: 0;
      padding: 0;
    }
    .app-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    .card {
      border: 1px solid #eaeaea;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 16px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }
    .button {
      background-color: #1677ff;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      margin-right: 8px;
      margin-bottom: 8px;
    }
    .button:hover {
      background-color: #4096ff;
    }
    .button.danger {
      background-color: #ff4d4f;
    }
    .button.danger:hover {
      background-color: #ff7875;
    }
    .log-panel {
      background-color: #f5f5f5;
      padding: 12px;
      border-radius: 4px;
      max-height: 200px;
      overflow-y: auto;
      font-family: monospace;
    }
    nav {
      background-color: #f0f2f5;
      padding: 12px;
      margin-bottom: 20px;
    }
    nav a {
      margin-right: 16px;
      color: #1677ff;
      text-decoration: none;
    }
    nav a:hover {
      text-decoration: underline;
    }
  </style>
</head>
<body>
  <div id="root"></div>
  <script type="module" src="/demo/index.ts"></script>
</body>
</html>
