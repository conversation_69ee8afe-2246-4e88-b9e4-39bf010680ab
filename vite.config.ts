import react from '@vitejs/plugin-react';
import {defineConfig} from 'vite';
import path from 'path';
import {viteMockServe} from 'vite-plugin-mock';

export default defineConfig({
  plugins: [
    react(),
    viteMockServe({
      mockPath: 'demo/mocks',
      enable: true,
      watchFiles: true,
      logger: true
    })
  ],
  server: {
    port: 3000,
    open: true
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './demo')
    }
  }
});
